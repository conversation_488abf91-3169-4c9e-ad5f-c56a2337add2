const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

class Database {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, 'voteawards.db');
    }

    // Initialize database connection
    async connect() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('Error opening database:', err.message);
                    reject(err);
                } else {
                    console.log('Connected to SQLite database');
                    // Enable foreign keys
                    this.db.run('PRAGMA foreign_keys = ON');
                    resolve();
                }
            });
        });
    }

    // Initialize database schema
    async initializeSchema() {
        try {
            const schemaPath = path.join(__dirname, 'schema.sql');
            const schema = fs.readFileSync(schemaPath, 'utf8');
            
            // Split schema into individual statements
            const statements = schema.split(';').filter(stmt => stmt.trim());
            
            for (const statement of statements) {
                if (statement.trim()) {
                    await this.run(statement);
                }
            }
            
            console.log('Database schema initialized successfully');
        } catch (error) {
            console.error('Error initializing schema:', error);
            throw error;
        }
    }

    // Execute a query that doesn't return data
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('Database run error:', err.message);
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    // Execute a query that returns a single row
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('Database get error:', err.message);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // Execute a query that returns multiple rows
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('Database all error:', err.message);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // Execute multiple queries in a transaction
    async transaction(queries) {
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                this.db.run('BEGIN TRANSACTION');
                
                const results = [];
                let hasError = false;
                
                const executeNext = (index) => {
                    if (index >= queries.length) {
                        if (hasError) {
                            this.db.run('ROLLBACK', () => {
                                reject(new Error('Transaction rolled back due to error'));
                            });
                        } else {
                            this.db.run('COMMIT', (err) => {
                                if (err) {
                                    reject(err);
                                } else {
                                    resolve(results);
                                }
                            });
                        }
                        return;
                    }
                    
                    const { sql, params } = queries[index];
                    this.db.run(sql, params, function(err) {
                        if (err) {
                            hasError = true;
                            console.error(`Transaction error at query ${index}:`, err.message);
                        } else {
                            results.push({ id: this.lastID, changes: this.changes });
                        }
                        executeNext(index + 1);
                    });
                };
                
                executeNext(0);
            });
        });
    }

    // Close database connection
    close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err.message);
                        reject(err);
                    } else {
                        console.log('Database connection closed');
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }

    // Utility method to check if table exists
    async tableExists(tableName) {
        const result = await this.get(
            "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
            [tableName]
        );
        return !!result;
    }

    // Utility method to get table info
    async getTableInfo(tableName) {
        return await this.all(`PRAGMA table_info(${tableName})`);
    }

    // Backup database
    async backup(backupPath) {
        return new Promise((resolve, reject) => {
            const backup = new sqlite3.Database(backupPath);
            this.db.backup(backup, (err) => {
                backup.close();
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }
}

// Create singleton instance
const database = new Database();

// Initialize database on module load
const initDatabase = async () => {
    try {
        await database.connect();
        await database.initializeSchema();
        console.log('Database initialization completed');
    } catch (error) {
        console.error('Failed to initialize database:', error);
        process.exit(1);
    }
};

// Export database instance and initialization function
module.exports = {
    db: database,
    initDatabase
};
