{"name": "voteawards-backend", "version": "1.0.0", "description": "Backend API for VoteAwards voting application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["voting", "awards", "api", "express"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.6.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.1.10"}}