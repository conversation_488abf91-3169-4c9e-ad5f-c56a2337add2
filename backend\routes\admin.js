const express = require('express');
const { db } = require('../database/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get dashboard overview statistics
router.get('/dashboard', authenticateToken, requireAdmin, async (req, res) => {
    try {
        // Get overall statistics
        const stats = await db.get(`
            SELECT 
                (SELECT COUNT(*) FROM users WHERE is_active = 1) as total_users,
                (SELECT COUNT(*) FROM users WHERE role = 'organizer' AND is_active = 1) as total_organizers,
                (SELECT COUNT(*) FROM events WHERE is_active = 1) as total_events,
                (SELECT COUNT(*) FROM events WHERE is_active = 1 AND start_date <= datetime('now') AND end_date > datetime('now')) as active_events,
                (SELECT COUNT(*) FROM nominees WHERE is_active = 1) as total_nominees,
                (SELECT COALESCE(SUM(vote_count), 0) FROM votes) as total_votes,
                (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE payment_status = 'completed') as total_revenue,
                (SELECT COUNT(*) FROM payments WHERE payment_status = 'completed') as completed_payments
        `);

        // Get recent activity
        const recentUsers = await db.all(`
            SELECT id, name, email, role, created_at
            FROM users
            ORDER BY created_at DESC
            LIMIT 5
        `);

        const recentEvents = await db.all(`
            SELECT 
                e.id, e.title, e.start_date, e.end_date, e.total_votes,
                u.name as organizer_name
            FROM events e
            JOIN users u ON e.organizer_id = u.id
            WHERE e.is_active = 1
            ORDER BY e.created_at DESC
            LIMIT 5
        `);

        const recentPayments = await db.all(`
            SELECT 
                p.id, p.amount, p.vote_count, p.payment_status, p.created_at,
                u.name as user_name,
                e.title as event_title
            FROM payments p
            JOIN users u ON p.user_id = u.id
            JOIN events e ON p.event_id = e.id
            ORDER BY p.created_at DESC
            LIMIT 5
        `);

        // Get monthly revenue data for charts
        const monthlyRevenue = await db.all(`
            SELECT 
                strftime('%Y-%m', created_at) as month,
                COALESCE(SUM(amount), 0) as revenue,
                COUNT(*) as payment_count
            FROM payments
            WHERE payment_status = 'completed'
                AND created_at >= date('now', '-12 months')
            GROUP BY strftime('%Y-%m', created_at)
            ORDER BY month
        `);

        // Get top events by votes
        const topEvents = await db.all(`
            SELECT 
                e.id, e.title, e.total_votes, e.total_revenue,
                u.name as organizer_name
            FROM events e
            JOIN users u ON e.organizer_id = u.id
            WHERE e.is_active = 1
            ORDER BY e.total_votes DESC
            LIMIT 5
        `);

        res.json({
            success: true,
            stats,
            recent_activity: {
                users: recentUsers,
                events: recentEvents,
                payments: recentPayments
            },
            charts: {
                monthly_revenue: monthlyRevenue
            },
            top_events: topEvents
        });

    } catch (error) {
        console.error('Get admin dashboard error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get system settings
router.get('/settings', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const settings = await db.all('SELECT * FROM settings ORDER BY key');
        
        res.json({
            success: true,
            settings
        });

    } catch (error) {
        console.error('Get settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Update system setting
router.put('/settings/:key', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const { key } = req.params;
        const { value } = req.body;

        if (!value) {
            return res.status(400).json({
                success: false,
                message: 'Value is required'
            });
        }

        // Check if setting exists
        const setting = await db.get('SELECT * FROM settings WHERE key = ?', [key]);
        
        if (setting) {
            // Update existing setting
            await db.run(
                'UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?',
                [value, key]
            );
        } else {
            // Create new setting
            await db.run(
                'INSERT INTO settings (key, value) VALUES (?, ?)',
                [key, value]
            );
        }

        res.json({
            success: true,
            message: 'Setting updated successfully'
        });

    } catch (error) {
        console.error('Update setting error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get audit logs
router.get('/audit-logs', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const { page = 1, limit = 20, action, table_name, user_id } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE 1=1';
        let params = [];

        if (action) {
            whereClause += ' AND action = ?';
            params.push(action);
        }

        if (table_name) {
            whereClause += ' AND table_name = ?';
            params.push(table_name);
        }

        if (user_id) {
            whereClause += ' AND user_id = ?';
            params.push(user_id);
        }

        const logs = await db.all(`
            SELECT 
                a.*,
                u.name as user_name,
                u.email as user_email
            FROM audit_logs a
            LEFT JOIN users u ON a.user_id = u.id
            ${whereClause}
            ORDER BY a.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, parseInt(limit), offset]);

        // Get total count
        const { total } = await db.get(
            `SELECT COUNT(*) as total FROM audit_logs ${whereClause}`,
            params
        );

        res.json({
            success: true,
            logs,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Get audit logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get system analytics
router.get('/analytics', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const { period = '30d' } = req.query;

        let dateFilter = '';
        if (period === '7d') {
            dateFilter = "AND created_at >= date('now', '-7 days')";
        } else if (period === '30d') {
            dateFilter = "AND created_at >= date('now', '-30 days')";
        } else if (period === '90d') {
            dateFilter = "AND created_at >= date('now', '-90 days')";
        } else if (period === '1y') {
            dateFilter = "AND created_at >= date('now', '-1 year')";
        }

        // User registration trends
        const userTrends = await db.all(`
            SELECT 
                date(created_at) as date,
                COUNT(*) as new_users
            FROM users
            WHERE 1=1 ${dateFilter}
            GROUP BY date(created_at)
            ORDER BY date
        `);

        // Event creation trends
        const eventTrends = await db.all(`
            SELECT 
                date(created_at) as date,
                COUNT(*) as new_events
            FROM events
            WHERE is_active = 1 ${dateFilter}
            GROUP BY date(created_at)
            ORDER BY date
        `);

        // Revenue trends
        const revenueTrends = await db.all(`
            SELECT 
                date(created_at) as date,
                COALESCE(SUM(amount), 0) as revenue,
                COUNT(*) as payment_count
            FROM payments
            WHERE payment_status = 'completed' ${dateFilter}
            GROUP BY date(created_at)
            ORDER BY date
        `);

        // Vote trends
        const voteTrends = await db.all(`
            SELECT 
                date(created_at) as date,
                COUNT(*) as votes_cast,
                COALESCE(SUM(vote_count), 0) as total_vote_count
            FROM votes
            WHERE 1=1 ${dateFilter}
            GROUP BY date(created_at)
            ORDER BY date
        `);

        // Top performing events
        const topEvents = await db.all(`
            SELECT 
                e.id, e.title, e.total_votes, e.total_revenue,
                u.name as organizer_name,
                COUNT(DISTINCT v.user_id) as unique_voters
            FROM events e
            JOIN users u ON e.organizer_id = u.id
            LEFT JOIN votes v ON e.id = v.event_id
            WHERE e.is_active = 1
            GROUP BY e.id
            ORDER BY e.total_revenue DESC
            LIMIT 10
        `);

        // User engagement metrics
        const engagement = await db.get(`
            SELECT 
                COUNT(DISTINCT v.user_id) as active_voters,
                COUNT(DISTINCT e.organizer_id) as active_organizers,
                COALESCE(AVG(votes_per_user.vote_count), 0) as avg_votes_per_user,
                COALESCE(AVG(events_per_organizer.event_count), 0) as avg_events_per_organizer
            FROM (SELECT 1) dummy
            LEFT JOIN votes v ON 1=1
            LEFT JOIN events e ON e.is_active = 1
            LEFT JOIN (
                SELECT user_id, COUNT(*) as vote_count
                FROM votes
                GROUP BY user_id
            ) votes_per_user ON 1=1
            LEFT JOIN (
                SELECT organizer_id, COUNT(*) as event_count
                FROM events
                WHERE is_active = 1
                GROUP BY organizer_id
            ) events_per_organizer ON 1=1
        `);

        res.json({
            success: true,
            period,
            trends: {
                users: userTrends,
                events: eventTrends,
                revenue: revenueTrends,
                votes: voteTrends
            },
            top_events: topEvents,
            engagement
        });

    } catch (error) {
        console.error('Get analytics error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Export data (admin only)
router.get('/export/:type', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const { type } = req.params;
        const { format = 'json' } = req.query;

        let data = [];
        let filename = '';

        switch (type) {
            case 'users':
                data = await db.all(`
                    SELECT id, name, email, role, phone, is_active, created_at
                    FROM users
                    ORDER BY created_at DESC
                `);
                filename = 'users_export';
                break;

            case 'events':
                data = await db.all(`
                    SELECT 
                        e.*, u.name as organizer_name
                    FROM events e
                    JOIN users u ON e.organizer_id = u.id
                    ORDER BY e.created_at DESC
                `);
                filename = 'events_export';
                break;

            case 'payments':
                data = await db.all(`
                    SELECT 
                        p.*, u.name as user_name, e.title as event_title
                    FROM payments p
                    JOIN users u ON p.user_id = u.id
                    JOIN events e ON p.event_id = e.id
                    ORDER BY p.created_at DESC
                `);
                filename = 'payments_export';
                break;

            case 'votes':
                data = await db.all(`
                    SELECT 
                        v.*, u.name as user_name, n.name as nominee_name, e.title as event_title
                    FROM votes v
                    JOIN users u ON v.user_id = u.id
                    JOIN nominees n ON v.nominee_id = n.id
                    JOIN events e ON v.event_id = e.id
                    ORDER BY v.created_at DESC
                `);
                filename = 'votes_export';
                break;

            default:
                return res.status(400).json({
                    success: false,
                    message: 'Invalid export type'
                });
        }

        if (format === 'csv') {
            // Convert to CSV format
            if (data.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'No data to export'
                });
            }

            const headers = Object.keys(data[0]);
            const csvContent = [
                headers.join(','),
                ...data.map(row => 
                    headers.map(header => 
                        JSON.stringify(row[header] || '')
                    ).join(',')
                )
            ].join('\n');

            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
            res.send(csvContent);
        } else {
            // JSON format
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
            res.json({
                success: true,
                data,
                exported_at: new Date().toISOString(),
                total_records: data.length
            });
        }

    } catch (error) {
        console.error('Export data error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
