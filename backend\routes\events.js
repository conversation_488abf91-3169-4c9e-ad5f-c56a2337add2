const express = require('express');
const multer = require('multer');
const path = require('path');
const { db } = require('../database/database');
const { authenticateToken, requireOrganizer, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/events/');
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'event-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5242880 // 5MB
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Only image files are allowed'));
        }
    }
});

// Get all events (public)
router.get('/', optionalAuth, async (req, res) => {
    try {
        const { page = 1, limit = 10, category, status = 'active', search } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE e.is_active = 1';
        let params = [];

        // Filter by status
        if (status === 'upcoming') {
            whereClause += ' AND e.start_date > datetime("now")';
        } else if (status === 'ongoing') {
            whereClause += ' AND e.start_date <= datetime("now") AND e.end_date > datetime("now")';
        } else if (status === 'ended') {
            whereClause += ' AND e.end_date <= datetime("now")';
        }

        // Filter by category
        if (category) {
            whereClause += ' AND EXISTS (SELECT 1 FROM event_categories ec JOIN categories c ON ec.category_id = c.id WHERE ec.event_id = e.id AND c.name = ?)';
            params.push(category);
        }

        // Search filter
        if (search) {
            whereClause += ' AND (e.title LIKE ? OR e.description LIKE ?)';
            params.push(`%${search}%`, `%${search}%`);
        }

        const query = `
            SELECT 
                e.*,
                u.name as organizer_name,
                COUNT(DISTINCT n.id) as nominee_count,
                COALESCE(SUM(n.vote_count), 0) as total_votes
            FROM events e
            LEFT JOIN users u ON e.organizer_id = u.id
            LEFT JOIN nominees n ON e.id = n.event_id AND n.is_active = 1
            ${whereClause}
            GROUP BY e.id
            ORDER BY e.created_at DESC
            LIMIT ? OFFSET ?
        `;

        params.push(parseInt(limit), offset);
        const events = await db.all(query, params);

        // Get total count for pagination
        const countQuery = `
            SELECT COUNT(DISTINCT e.id) as total
            FROM events e
            ${whereClause.replace('GROUP BY e.id', '')}
        `;
        const countParams = params.slice(0, -2); // Remove limit and offset
        const { total } = await db.get(countQuery, countParams);

        res.json({
            success: true,
            events,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Get events error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get categories
router.get('/categories', async (req, res) => {
    try {
        const categories = await db.all('SELECT * FROM categories ORDER BY name');

        res.json({
            success: true,
            categories
        });
    } catch (error) {
        console.error('Error fetching categories:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch categories'
        });
    }
});

// Get single event by ID (public)
router.get('/:id', optionalAuth, async (req, res) => {
    try {
        const eventId = req.params.id;

        const event = await db.get(`
            SELECT 
                e.*,
                u.name as organizer_name,
                u.email as organizer_email
            FROM events e
            LEFT JOIN users u ON e.organizer_id = u.id
            WHERE e.id = ? AND e.is_active = 1
        `, [eventId]);

        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Event not found'
            });
        }

        // Get event categories
        const categories = await db.all(`
            SELECT c.id, c.name, c.icon
            FROM categories c
            JOIN event_categories ec ON c.id = ec.category_id
            WHERE ec.event_id = ?
        `, [eventId]);

        // Get nominees for this event
        const nominees = await db.all(`
            SELECT 
                id, name, description, profile_image, vote_count, total_amount_received
            FROM nominees
            WHERE event_id = ? AND is_active = 1
            ORDER BY vote_count DESC
        `, [eventId]);

        res.json({
            success: true,
            event: {
                ...event,
                categories,
                nominees
            }
        });

    } catch (error) {
        console.error('Get event error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Create new event (organizers only)
router.post('/', authenticateToken, requireOrganizer, upload.single('banner'), async (req, res) => {
    try {
        const { title, description, vote_price, start_date, end_date, categories } = req.body;
        const organizerId = req.user.userId;

        // Validation
        if (!title || !description || !start_date || !end_date) {
            return res.status(400).json({
                success: false,
                message: 'Title, description, start date, and end date are required'
            });
        }

        const startDate = new Date(start_date);
        const endDate = new Date(end_date);
        const now = new Date();

        if (startDate <= now) {
            return res.status(400).json({
                success: false,
                message: 'Start date must be in the future'
            });
        }

        if (endDate <= startDate) {
            return res.status(400).json({
                success: false,
                message: 'End date must be after start date'
            });
        }

        const bannerImage = req.file ? `/uploads/events/${req.file.filename}` : null;
        const eventVotePrice = vote_price || process.env.DEFAULT_VOTE_PRICE || 1.00;

        // Create event
        const result = await db.run(`
            INSERT INTO events (title, description, organizer_id, vote_price, banner_image, start_date, end_date)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [title, description, organizerId, eventVotePrice, bannerImage, start_date, end_date]);

        const eventId = result.id;

        // Add categories if provided
        if (categories && Array.isArray(categories)) {
            for (const categoryId of categories) {
                await db.run(
                    'INSERT INTO event_categories (event_id, category_id) VALUES (?, ?)',
                    [eventId, categoryId]
                );
            }
        }

        // Get created event with details
        const event = await db.get(`
            SELECT
                e.*,
                u.name as organizer_name
            FROM events e
            LEFT JOIN users u ON e.organizer_id = u.id
            WHERE e.id = ?
        `, [eventId]);

        res.status(201).json({
            success: true,
            message: 'Event created successfully',
            event
        });

    } catch (error) {
        console.error('Create event error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Update event (organizer or admin only)
router.put('/:id', authenticateToken, upload.single('banner'), async (req, res) => {
    try {
        const eventId = req.params.id;
        const { title, description, vote_price, start_date, end_date, categories } = req.body;

        // Check if event exists and user has permission
        const event = await db.get('SELECT * FROM events WHERE id = ?', [eventId]);
        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Event not found'
            });
        }

        // Check ownership (organizer can only edit their own events, admin can edit any)
        if (req.user.role !== 'admin' && event.organizer_id !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'You can only edit your own events'
            });
        }

        // Prepare update data
        const updateData = {};
        if (title) updateData.title = title;
        if (description) updateData.description = description;
        if (vote_price) updateData.vote_price = vote_price;
        if (start_date) updateData.start_date = start_date;
        if (end_date) updateData.end_date = end_date;
        if (req.file) updateData.banner_image = `/uploads/events/${req.file.filename}`;

        // Build update query
        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const updateValues = Object.values(updateData);

        if (updateFields) {
            await db.run(
                `UPDATE events SET ${updateFields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
                [...updateValues, eventId]
            );
        }

        // Update categories if provided
        if (categories && Array.isArray(categories)) {
            // Remove existing categories
            await db.run('DELETE FROM event_categories WHERE event_id = ?', [eventId]);

            // Add new categories
            for (const categoryId of categories) {
                await db.run(
                    'INSERT INTO event_categories (event_id, category_id) VALUES (?, ?)',
                    [eventId, categoryId]
                );
            }
        }

        // Get updated event
        const updatedEvent = await db.get(`
            SELECT
                e.*,
                u.name as organizer_name
            FROM events e
            LEFT JOIN users u ON e.organizer_id = u.id
            WHERE e.id = ?
        `, [eventId]);

        res.json({
            success: true,
            message: 'Event updated successfully',
            event: updatedEvent
        });

    } catch (error) {
        console.error('Update event error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Delete event (organizer or admin only)
router.delete('/:id', authenticateToken, async (req, res) => {
    try {
        const eventId = req.params.id;

        // Check if event exists and user has permission
        const event = await db.get('SELECT * FROM events WHERE id = ?', [eventId]);
        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Event not found'
            });
        }

        // Check ownership
        if (req.user.role !== 'admin' && event.organizer_id !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'You can only delete your own events'
            });
        }

        // Soft delete (set is_active to 0)
        await db.run(
            'UPDATE events SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [eventId]
        );

        res.json({
            success: true,
            message: 'Event deleted successfully'
        });

    } catch (error) {
        console.error('Delete event error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
