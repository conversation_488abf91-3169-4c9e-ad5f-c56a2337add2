// Authentication functionality
class AuthManager {
    constructor() {
        this.token = localStorage.getItem('token');
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        this.init();
    }

    init() {
        // Initialize auth state on page load
        this.updateNavigation();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Register form
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // Logout button
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('logout-btn')) {
                this.logout();
            }
        });
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const loginData = {
            email: formData.get('email'),
            password: formData.get('password')
        };

        try {
            showLoading(e.target.querySelector('button[type="submit"]'));
            
            const response = await fetch(`${API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(loginData)
            });

            const result = await response.json();

            if (result.success) {
                this.setAuthData(result.token, result.user);
                showNotification('Login successful!', 'success');
                
                // Redirect based on user role
                setTimeout(() => {
                    if (result.user.role === 'admin') {
                        window.location.href = 'admin/dashboard.html';
                    } else if (result.user.role === 'organizer') {
                        window.location.href = 'organizer/dashboard.html';
                    } else {
                        window.location.href = 'index.html';
                    }
                }, 1000);
            } else {
                showNotification(result.message || 'Login failed', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            showNotification('Network error. Please try again.', 'error');
        } finally {
            hideLoading(e.target.querySelector('button[type="submit"]'));
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');

        // Validate password confirmation
        if (password !== confirmPassword) {
            showNotification('Passwords do not match', 'error');
            return;
        }

        const registerData = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            role: formData.get('role'),
            password: password
        };

        try {
            showLoading(e.target.querySelector('button[type="submit"]'));
            
            const response = await fetch(`${API_BASE_URL}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(registerData)
            });

            const result = await response.json();

            if (result.success) {
                showNotification('Registration successful! Please login.', 'success');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1500);
            } else {
                showNotification(result.message || 'Registration failed', 'error');
            }
        } catch (error) {
            console.error('Registration error:', error);
            showNotification('Network error. Please try again.', 'error');
        } finally {
            hideLoading(e.target.querySelector('button[type="submit"]'));
        }
    }

    setAuthData(token, user) {
        this.token = token;
        this.user = user;
        localStorage.setItem('token', token);
        localStorage.setItem('user', JSON.stringify(user));
        this.updateNavigation();
    }

    logout() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        this.updateNavigation();
        showNotification('Logged out successfully', 'success');
        
        // Redirect to home page
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1000);
    }

    updateNavigation() {
        const navAuth = document.getElementById('nav-auth');
        if (!navAuth) return;

        if (this.isAuthenticated()) {
            navAuth.innerHTML = `
                <div class="user-menu">
                    <span class="user-name">
                        <i class="fas fa-user"></i> ${this.user.name}
                    </span>
                    <div class="user-dropdown">
                        ${this.user.role === 'admin' ? '<a href="admin/dashboard.html"><i class="fas fa-cog"></i> Admin Panel</a>' : ''}
                        ${this.user.role === 'organizer' ? '<a href="organizer/dashboard.html"><i class="fas fa-chart-bar"></i> Dashboard</a>' : ''}
                        <a href="profile.html"><i class="fas fa-user-edit"></i> Profile</a>
                        <a href="my-votes.html"><i class="fas fa-vote-yea"></i> My Votes</a>
                        <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            `;
        } else {
            navAuth.innerHTML = `
                <a href="login.html" class="nav-link">Login</a>
                <a href="register.html" class="nav-link">Register</a>
            `;
        }
    }

    isAuthenticated() {
        return this.token && this.user;
    }

    hasRole(role) {
        return this.isAuthenticated() && this.user.role === role;
    }

    getAuthHeaders() {
        if (!this.token) return {};
        return {
            'Authorization': `Bearer ${this.token}`
        };
    }

    async makeAuthenticatedRequest(url, options = {}) {
        const headers = {
            'Content-Type': 'application/json',
            ...this.getAuthHeaders(),
            ...options.headers
        };

        const response = await fetch(url, {
            ...options,
            headers
        });

        // Handle unauthorized responses
        if (response.status === 401) {
            this.logout();
            throw new Error('Authentication required');
        }

        return response;
    }

    // Check if user needs to be redirected based on current page
    checkPageAccess() {
        const currentPage = window.location.pathname.split('/').pop();
        
        // Pages that require authentication
        const protectedPages = ['profile.html', 'my-votes.html'];
        
        // Admin pages
        const adminPages = ['admin/dashboard.html', 'admin/users.html', 'admin/analytics.html'];
        
        // Organizer pages
        const organizerPages = ['organizer/dashboard.html', 'organizer/events.html'];

        if (protectedPages.includes(currentPage) && !this.isAuthenticated()) {
            showNotification('Please login to access this page', 'warning');
            window.location.href = 'login.html';
            return false;
        }

        if (adminPages.some(page => currentPage.includes(page)) && !this.hasRole('admin')) {
            showNotification('Access denied. Admin privileges required.', 'error');
            window.location.href = 'index.html';
            return false;
        }

        if (organizerPages.some(page => currentPage.includes(page)) && 
            !this.hasRole('organizer') && !this.hasRole('admin')) {
            showNotification('Access denied. Organizer privileges required.', 'error');
            window.location.href = 'index.html';
            return false;
        }

        return true;
    }
}

// Initialize auth manager
const authManager = new AuthManager();

// Check page access on load
document.addEventListener('DOMContentLoaded', () => {
    authManager.checkPageAccess();
});

// Export for use in other modules
window.authManager = authManager;
