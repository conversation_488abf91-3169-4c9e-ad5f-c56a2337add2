const express = require('express');
const multer = require('multer');
const path = require('path');
const { db } = require('../database/database');
const { authenticateToken, requireOrganizer, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Configure multer for nominee profile images
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/nominees/');
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'nominee-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5242880 // 5MB
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Only image files are allowed'));
        }
    }
});

// Get nominees for an event (public)
router.get('/event/:eventId', optionalAuth, async (req, res) => {
    try {
        const eventId = req.params.eventId;
        const { page = 1, limit = 10, sort = 'votes' } = req.query;
        const offset = (page - 1) * limit;

        // Check if event exists
        const event = await db.get('SELECT id FROM events WHERE id = ? AND is_active = 1', [eventId]);
        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Event not found'
            });
        }

        // Determine sort order
        let orderBy = 'vote_count DESC';
        if (sort === 'name') {
            orderBy = 'name ASC';
        } else if (sort === 'recent') {
            orderBy = 'created_at DESC';
        }

        const nominees = await db.all(`
            SELECT 
                id, name, description, bio, profile_image, website_url, 
                social_media, achievements, vote_count, total_amount_received,
                created_at
            FROM nominees
            WHERE event_id = ? AND is_active = 1
            ORDER BY ${orderBy}
            LIMIT ? OFFSET ?
        `, [eventId, parseInt(limit), offset]);

        // Get total count
        const { total } = await db.get(
            'SELECT COUNT(*) as total FROM nominees WHERE event_id = ? AND is_active = 1',
            [eventId]
        );

        res.json({
            success: true,
            nominees,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Get nominees error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get single nominee by ID (public)
router.get('/:id', optionalAuth, async (req, res) => {
    try {
        const nomineeId = req.params.id;

        const nominee = await db.get(`
            SELECT 
                n.*,
                e.title as event_title,
                e.vote_price,
                e.start_date,
                e.end_date,
                e.organizer_id
            FROM nominees n
            JOIN events e ON n.event_id = e.id
            WHERE n.id = ? AND n.is_active = 1 AND e.is_active = 1
        `, [nomineeId]);

        if (!nominee) {
            return res.status(404).json({
                success: false,
                message: 'Nominee not found'
            });
        }

        // Parse social media JSON if it exists
        if (nominee.social_media) {
            try {
                nominee.social_media = JSON.parse(nominee.social_media);
            } catch (e) {
                nominee.social_media = {};
            }
        }

        // Get recent votes for this nominee (for activity feed)
        const recentVotes = await db.all(`
            SELECT 
                v.vote_count,
                v.created_at,
                u.name as voter_name
            FROM votes v
            LEFT JOIN users u ON v.user_id = u.id
            WHERE v.nominee_id = ?
            ORDER BY v.created_at DESC
            LIMIT 10
        `, [nomineeId]);

        res.json({
            success: true,
            nominee: {
                ...nominee,
                recent_votes: recentVotes
            }
        });

    } catch (error) {
        console.error('Get nominee error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Create nominee (organizer only)
router.post('/', authenticateToken, requireOrganizer, upload.single('profile_image'), async (req, res) => {
    try {
        const {
            event_id,
            name,
            description,
            bio,
            website_url,
            social_media,
            achievements
        } = req.body;

        // Validation
        if (!event_id || !name || !description) {
            return res.status(400).json({
                success: false,
                message: 'Event ID, name, and description are required'
            });
        }

        // Check if event exists and user owns it (or is admin)
        const event = await db.get('SELECT organizer_id FROM events WHERE id = ? AND is_active = 1', [event_id]);
        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Event not found'
            });
        }

        if (req.user.role !== 'admin' && event.organizer_id !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'You can only add nominees to your own events'
            });
        }

        const profileImage = req.file ? `/uploads/nominees/${req.file.filename}` : null;
        const socialMediaJson = social_media ? JSON.stringify(social_media) : null;

        // Create nominee
        const result = await db.run(`
            INSERT INTO nominees (
                event_id, name, description, bio, profile_image, 
                website_url, social_media, achievements
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            event_id, name, description, bio, profileImage,
            website_url, socialMediaJson, achievements
        ]);

        // Get created nominee
        const nominee = await db.get('SELECT * FROM nominees WHERE id = ?', [result.id]);

        res.status(201).json({
            success: true,
            message: 'Nominee created successfully',
            nominee
        });

    } catch (error) {
        console.error('Create nominee error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Update nominee (organizer or admin only)
router.put('/:id', authenticateToken, upload.single('profile_image'), async (req, res) => {
    try {
        const nomineeId = req.params.id;
        const {
            name,
            description,
            bio,
            website_url,
            social_media,
            achievements
        } = req.body;

        // Check if nominee exists and user has permission
        const nominee = await db.get(`
            SELECT n.*, e.organizer_id
            FROM nominees n
            JOIN events e ON n.event_id = e.id
            WHERE n.id = ?
        `, [nomineeId]);

        if (!nominee) {
            return res.status(404).json({
                success: false,
                message: 'Nominee not found'
            });
        }

        // Check ownership
        if (req.user.role !== 'admin' && nominee.organizer_id !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'You can only edit nominees from your own events'
            });
        }

        // Prepare update data
        const updateData = {};
        if (name) updateData.name = name;
        if (description) updateData.description = description;
        if (bio) updateData.bio = bio;
        if (website_url) updateData.website_url = website_url;
        if (achievements) updateData.achievements = achievements;
        if (social_media) updateData.social_media = JSON.stringify(social_media);
        if (req.file) updateData.profile_image = `/uploads/nominees/${req.file.filename}`;

        // Build update query
        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const updateValues = Object.values(updateData);

        if (updateFields) {
            await db.run(
                `UPDATE nominees SET ${updateFields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
                [...updateValues, nomineeId]
            );
        }

        // Get updated nominee
        const updatedNominee = await db.get('SELECT * FROM nominees WHERE id = ?', [nomineeId]);

        res.json({
            success: true,
            message: 'Nominee updated successfully',
            nominee: updatedNominee
        });

    } catch (error) {
        console.error('Update nominee error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Delete nominee (organizer or admin only)
router.delete('/:id', authenticateToken, async (req, res) => {
    try {
        const nomineeId = req.params.id;

        // Check if nominee exists and user has permission
        const nominee = await db.get(`
            SELECT n.*, e.organizer_id
            FROM nominees n
            JOIN events e ON n.event_id = e.id
            WHERE n.id = ?
        `, [nomineeId]);

        if (!nominee) {
            return res.status(404).json({
                success: false,
                message: 'Nominee not found'
            });
        }

        // Check ownership
        if (req.user.role !== 'admin' && nominee.organizer_id !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'You can only delete nominees from your own events'
            });
        }

        // Soft delete
        await db.run(
            'UPDATE nominees SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [nomineeId]
        );

        res.json({
            success: true,
            message: 'Nominee deleted successfully'
        });

    } catch (error) {
        console.error('Delete nominee error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
