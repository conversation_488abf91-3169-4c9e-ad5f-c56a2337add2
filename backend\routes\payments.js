const express = require('express');
const { db } = require('../database/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get user's payment history
router.get('/my-payments', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { page = 1, limit = 10, status } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE p.user_id = ?';
        let params = [userId];

        // Filter by status
        if (status) {
            whereClause += ' AND p.payment_status = ?';
            params.push(status);
        }

        const payments = await db.all(`
            SELECT 
                p.*,
                e.title as event_title,
                e.vote_price
            FROM payments p
            JOIN events e ON p.event_id = e.id
            ${whereClause}
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, parseInt(limit), offset]);

        // Get total count
        const countParams = params.slice();
        const { total } = await db.get(
            `SELECT COUNT(*) as total FROM payments p ${whereClause}`,
            countParams
        );

        // Get user's payment statistics
        const stats = await db.get(`
            SELECT 
                COUNT(*) as total_payments,
                COALESCE(SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END), 0) as total_spent,
                COALESCE(SUM(CASE WHEN payment_status = 'completed' THEN vote_count ELSE 0 END), 0) as total_votes_purchased,
                COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments,
                COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_payments
            FROM payments
            WHERE user_id = ?
        `, [userId]);

        res.json({
            success: true,
            payments,
            stats,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Get user payments error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get payment statistics (admin only)
router.get('/stats/overview', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const { event_id, start_date, end_date } = req.query;

        let whereClause = 'WHERE 1=1';
        let params = [];

        if (event_id) {
            whereClause += ' AND event_id = ?';
            params.push(event_id);
        }

        if (start_date) {
            whereClause += ' AND created_at >= ?';
            params.push(start_date);
        }

        if (end_date) {
            whereClause += ' AND created_at <= ?';
            params.push(end_date);
        }

        const stats = await db.get(`
            SELECT
                COUNT(*) as total_payments,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
                COUNT(CASE WHEN status = 'refunded' THEN 1 END) as refunded_payments,
                COALESCE(SUM(CASE WHEN status = 'completed' THEN amount END), 0) as total_revenue,
                COALESCE(AVG(CASE WHEN status = 'completed' THEN amount END), 0) as average_payment
            FROM payments
            ${whereClause}
        `, params);

        res.json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('Get payment stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get payment by ID (user can only see their own, admin can see all)
router.get('/:id', authenticateToken, async (req, res) => {
    try {
        const paymentId = req.params.id;
        const userId = req.user.userId;
        const isAdmin = req.user.role === 'admin';

        let whereClause = 'WHERE p.id = ?';
        let params = [paymentId];

        if (!isAdmin) {
            whereClause += ' AND p.user_id = ?';
            params.push(userId);
        }

        const payment = await db.get(`
            SELECT 
                p.*,
                e.title as event_title,
                e.vote_price,
                u.name as user_name,
                u.email as user_email
            FROM payments p
            JOIN events e ON p.event_id = e.id
            JOIN users u ON p.user_id = u.id
            ${whereClause}
        `, params);

        if (!payment) {
            return res.status(404).json({
                success: false,
                message: 'Payment not found'
            });
        }

        // Get associated votes if payment is completed
        if (payment.payment_status === 'completed') {
            const votes = await db.all(`
                SELECT 
                    v.vote_count,
                    v.created_at,
                    n.name as nominee_name,
                    n.profile_image as nominee_image
                FROM votes v
                JOIN nominees n ON v.nominee_id = n.id
                WHERE v.payment_id = ?
                ORDER BY v.created_at DESC
            `, [paymentId]);

            payment.votes = votes;
        }

        res.json({
            success: true,
            payment
        });

    } catch (error) {
        console.error('Get payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get all payments (admin only)
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const { page = 1, limit = 10, status, event_id, user_id } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE 1=1';
        let params = [];

        // Filter by status
        if (status) {
            whereClause += ' AND p.payment_status = ?';
            params.push(status);
        }

        // Filter by event
        if (event_id) {
            whereClause += ' AND p.event_id = ?';
            params.push(event_id);
        }

        // Filter by user
        if (user_id) {
            whereClause += ' AND p.user_id = ?';
            params.push(user_id);
        }

        const payments = await db.all(`
            SELECT 
                p.*,
                e.title as event_title,
                u.name as user_name,
                u.email as user_email
            FROM payments p
            JOIN events e ON p.event_id = e.id
            JOIN users u ON p.user_id = u.id
            ${whereClause}
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, parseInt(limit), offset]);

        // Get total count
        const countParams = params.slice();
        const { total } = await db.get(
            `SELECT COUNT(*) as total FROM payments p ${whereClause}`,
            countParams
        );

        res.json({
            success: true,
            payments,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Get payments error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Update payment status (admin only)
router.put('/:id/status', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const paymentId = req.params.id;
        const { status } = req.body;

        if (!status || !['pending', 'completed', 'failed', 'refunded'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Valid status is required (pending, completed, failed, refunded)'
            });
        }

        // Check if payment exists
        const payment = await db.get('SELECT * FROM payments WHERE id = ?', [paymentId]);
        if (!payment) {
            return res.status(404).json({
                success: false,
                message: 'Payment not found'
            });
        }

        // Update payment status
        await db.run(
            'UPDATE payments SET payment_status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [status, paymentId]
        );

        // If payment is being marked as failed or refunded, we might need to reverse votes
        if ((status === 'failed' || status === 'refunded') && payment.payment_status === 'completed') {
            // Get associated votes
            const votes = await db.all('SELECT * FROM votes WHERE payment_id = ?', [paymentId]);
            
            for (const vote of votes) {
                // Reverse the vote counts
                await db.run(`
                    UPDATE nominees 
                    SET vote_count = vote_count - ?, 
                        total_amount_received = total_amount_received - ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                `, [vote.vote_count, vote.amount_paid, vote.nominee_id]);

                // Update event totals
                await db.run(`
                    UPDATE events 
                    SET total_votes = total_votes - ?,
                        total_revenue = total_revenue - ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                `, [vote.vote_count, vote.amount_paid, vote.event_id]);

                // Mark votes as cancelled (or delete them)
                await db.run('DELETE FROM votes WHERE id = ?', [vote.id]);
            }
        }

        res.json({
            success: true,
            message: 'Payment status updated successfully'
        });

    } catch (error) {
        console.error('Update payment status error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
