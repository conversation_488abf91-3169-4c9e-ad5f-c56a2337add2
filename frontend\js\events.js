// Events page functionality
class EventsManager {
    constructor() {
        this.currentPage = 1;
        this.eventsPerPage = 12;
        this.filters = {
            category: '',
            status: '',
            sort: 'start_date',
            search: ''
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadCategories();
        this.loadEvents();
    }

    setupEventListeners() {
        // Filter change events
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.filters.category = e.target.value;
            this.currentPage = 1;
            this.loadEvents();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.currentPage = 1;
            this.loadEvents();
        });

        document.getElementById('sortFilter').addEventListener('change', (e) => {
            this.filters.sort = e.target.value;
            this.currentPage = 1;
            this.loadEvents();
        });

        // Search with debounce
        let searchTimeout;
        document.getElementById('searchFilter').addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.filters.search = e.target.value;
                this.currentPage = 1;
                this.loadEvents();
            }, 500);
        });
    }

    async loadCategories() {
        try {
            const response = await fetch(`${API_BASE_URL}/events/categories`);
            const result = await response.json();

            if (result.success) {
                const categoryFilter = document.getElementById('categoryFilter');
                result.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categoryFilter.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    async loadEvents() {
        const loading = document.getElementById('loading');
        const eventsGrid = document.getElementById('eventsGrid');
        const emptyState = document.getElementById('emptyState');

        try {
            loading.style.display = 'block';
            eventsGrid.innerHTML = '';
            emptyState.style.display = 'none';

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.eventsPerPage,
                sort: this.filters.sort,
                ...this.filters.category && { category_id: this.filters.category },
                ...this.filters.status && { status: this.filters.status },
                ...this.filters.search && { search: this.filters.search }
            });

            const response = await fetch(`${API_BASE_URL}/events?${params}`);
            const result = await response.json();

            if (result.success) {
                if (result.events.length === 0) {
                    emptyState.style.display = 'block';
                } else {
                    this.renderEvents(result.events);
                    this.renderPagination(result.pagination);
                }
            } else {
                showNotification('Failed to load events', 'error');
            }
        } catch (error) {
            console.error('Error loading events:', error);
            showNotification('Network error. Please try again.', 'error');
        } finally {
            loading.style.display = 'none';
        }
    }

    renderEvents(events) {
        const eventsGrid = document.getElementById('eventsGrid');
        
        eventsGrid.innerHTML = events.map(event => {
            const startDate = new Date(event.start_date);
            const endDate = new Date(event.end_date);
            const now = new Date();
            
            let status = 'upcoming';
            let statusClass = 'status-upcoming';
            let statusText = 'Upcoming';
            
            if (now >= startDate && now <= endDate) {
                status = 'active';
                statusClass = 'status-active';
                statusText = 'Active';
            } else if (now > endDate) {
                status = 'ended';
                statusClass = 'status-ended';
                statusText = 'Ended';
            }

            return `
                <div class="event-card" onclick="viewEvent(${event.id})">
                    <div class="event-image">
                        <img src="${event.banner_image || 'images/default-event.jpg'}" 
                             alt="${event.title}" 
                             onerror="this.src='images/default-event.jpg'">
                        <div class="event-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="event-content">
                        <h3 class="event-title">${event.title}</h3>
                        <p class="event-description">${this.truncateText(event.description, 100)}</p>
                        
                        <div class="event-meta">
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span>${this.formatDateRange(startDate, endDate)}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-user"></i>
                                <span>${event.organizer_name}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-vote-yea"></i>
                                <span>${event.total_votes} votes</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-coins"></i>
                                <span>R${event.vote_price} per vote</span>
                            </div>
                        </div>
                        
                        <div class="event-actions">
                            <button class="btn btn-primary" onclick="event.stopPropagation(); viewEvent(${event.id})">
                                <i class="fas fa-eye"></i> View Event
                            </button>
                            ${status === 'active' ? `
                                <button class="btn btn-secondary" onclick="event.stopPropagation(); viewNominees(${event.id})">
                                    <i class="fas fa-users"></i> Vote Now
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    renderPagination(pagination) {
        const paginationContainer = document.getElementById('pagination');
        
        if (pagination.pages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-controls">';
        
        // Previous button
        if (pagination.page > 1) {
            paginationHTML += `
                <button class="btn btn-outline" onclick="eventsManager.goToPage(${pagination.page - 1})">
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        if (startPage > 1) {
            paginationHTML += `<button class="btn btn-outline" onclick="eventsManager.goToPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.page ? 'btn-primary' : 'btn-outline';
            paginationHTML += `
                <button class="btn ${activeClass}" onclick="eventsManager.goToPage(${i})">${i}</button>
            `;
        }

        if (endPage < pagination.pages) {
            if (endPage < pagination.pages - 1) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }
            paginationHTML += `
                <button class="btn btn-outline" onclick="eventsManager.goToPage(${pagination.pages})">
                    ${pagination.pages}
                </button>
            `;
        }

        // Next button
        if (pagination.page < pagination.pages) {
            paginationHTML += `
                <button class="btn btn-outline" onclick="eventsManager.goToPage(${pagination.page + 1})">
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        
        // Add pagination info
        paginationHTML += `
            <div class="pagination-info">
                Showing ${((pagination.page - 1) * pagination.limit) + 1} to 
                ${Math.min(pagination.page * pagination.limit, pagination.total)} of 
                ${pagination.total} events
            </div>
        `;

        paginationContainer.innerHTML = paginationHTML;
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadEvents();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    }

    formatDateRange(startDate, endDate) {
        const options = { month: 'short', day: 'numeric' };
        const start = startDate.toLocaleDateString('en-US', options);
        const end = endDate.toLocaleDateString('en-US', options);
        
        if (startDate.getFullYear() !== endDate.getFullYear()) {
            return `${start}, ${startDate.getFullYear()} - ${end}, ${endDate.getFullYear()}`;
        } else if (startDate.getMonth() !== endDate.getMonth()) {
            return `${start} - ${end}, ${endDate.getFullYear()}`;
        } else if (startDate.getDate() !== endDate.getDate()) {
            return `${start} - ${endDate.getDate()}, ${endDate.getFullYear()}`;
        } else {
            return `${start}, ${endDate.getFullYear()}`;
        }
    }
}

// Global functions for event actions
function viewEvent(eventId) {
    window.location.href = `event-details.html?id=${eventId}`;
}

function viewNominees(eventId) {
    window.location.href = `nominees.html?event=${eventId}`;
}

// Initialize events manager
const eventsManager = new EventsManager();
