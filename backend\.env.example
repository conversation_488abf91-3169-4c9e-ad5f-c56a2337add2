# VoteAwards Backend Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:8080

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Database Configuration
DB_PATH=./database/voteawards.db

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Payment Gateway Configuration (for future implementation)
PAYFAST_MERCHANT_ID=your-payfast-merchant-id
PAYFAST_MERCHANT_KEY=your-payfast-merchant-key
PAYFAST_PASSPHRASE=your-payfast-passphrase
PAYFAST_SANDBOX=true

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Application Settings
APP_NAME=VoteAwards
DEFAULT_VOTE_PRICE=1.00
COMMISSION_RATE=0.05
CURRENCY=ZAR
TIMEZONE=Africa/Johannesburg

# Security Settings
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
