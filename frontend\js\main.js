// Main JavaScript file for VoteAwards app

// API Configuration
const API_BASE_URL = 'http://localhost:3000/api';

// Global state management
const AppState = {
    user: null,
    currentEvent: null,
    isLoggedIn: false
};

// Utility functions
const Utils = {
    // Make API requests
    async apiRequest(endpoint, options = {}) {
        const url = `${API_BASE_URL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // Add auth token if user is logged in
        const token = localStorage.getItem('authToken');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    },

    // Show notification messages
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        
        // Set background color based on type
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        notification.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    },

    // Format currency
    formatCurrency(amount) {
        return `R${amount.toFixed(2)}`;
    },

    // Format date
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-ZA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    },

    // Validate email
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Format date and time
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-ZA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    // Show/hide loading state
    showLoading(element, text = 'Loading...') {
        if (element) {
            element.disabled = true;
            element.dataset.originalText = element.innerHTML;
            element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
        }
    },

    hideLoading(element) {
        if (element && element.dataset.originalText) {
            element.disabled = false;
            element.innerHTML = element.dataset.originalText;
        }
    },

    // Truncate text
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    },

    // Get query parameter
    getQueryParam(param) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(param);
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Authentication functions
const Auth = {
    // Check if user is logged in
    checkAuth() {
        const token = localStorage.getItem('authToken');
        const user = localStorage.getItem('user');
        
        if (token && user) {
            AppState.user = JSON.parse(user);
            AppState.isLoggedIn = true;
            this.updateNavigation();
            return true;
        }
        return false;
    },

    // Login user
    async login(email, password) {
        try {
            const response = await Utils.apiRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify({ email, password })
            });

            localStorage.setItem('authToken', response.token);
            localStorage.setItem('user', JSON.stringify(response.user));
            
            AppState.user = response.user;
            AppState.isLoggedIn = true;
            
            this.updateNavigation();
            Utils.showNotification('Login successful!', 'success');
            
            // Redirect based on user role
            if (response.user.role === 'admin') {
                window.location.href = 'admin/dashboard.html';
            } else if (response.user.role === 'organizer') {
                window.location.href = 'organizer/dashboard.html';
            } else {
                window.location.href = 'events.html';
            }
            
            return response;
        } catch (error) {
            Utils.showNotification(error.message, 'error');
            throw error;
        }
    },

    // Register user
    async register(userData) {
        try {
            const response = await Utils.apiRequest('/auth/register', {
                method: 'POST',
                body: JSON.stringify(userData)
            });

            Utils.showNotification('Registration successful! Please login.', 'success');
            return response;
        } catch (error) {
            Utils.showNotification(error.message, 'error');
            throw error;
        }
    },

    // Logout user
    logout() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        
        AppState.user = null;
        AppState.isLoggedIn = false;
        
        this.updateNavigation();
        Utils.showNotification('Logged out successfully', 'info');
        window.location.href = 'index.html';
    },

    // Update navigation based on auth state
    updateNavigation() {
        const navMenu = document.getElementById('nav-menu');
        if (!navMenu) return;

        if (AppState.isLoggedIn) {
            const userRole = AppState.user.role;
            let dashboardLink = 'events.html';
            let dashboardText = 'Events';

            if (userRole === 'admin') {
                dashboardLink = 'admin/dashboard.html';
                dashboardText = 'Admin Dashboard';
            } else if (userRole === 'organizer') {
                dashboardLink = 'organizer/dashboard.html';
                dashboardText = 'My Dashboard';
            }

            navMenu.innerHTML = `
                <a href="index.html" class="nav-link">Home</a>
                <a href="events.html" class="nav-link">Events</a>
                <a href="${dashboardLink}" class="nav-link">${dashboardText}</a>
                <a href="#" class="nav-link" onclick="Auth.logout()">Logout (${AppState.user.name})</a>
            `;
        } else {
            navMenu.innerHTML = `
                <a href="index.html" class="nav-link">Home</a>
                <a href="events.html" class="nav-link">Events</a>
                <a href="login.html" class="nav-link">Login</a>
                <a href="register.html" class="nav-link">Register</a>
            `;
        }
    }
};

// Mobile navigation toggle
document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // Check authentication on page load
    Auth.checkAuth();

    // Add CSS for notifications
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
});

// Global utility functions (for backward compatibility)
function showNotification(message, type = 'info') {
    return Utils.showNotification(message, type);
}

function showLoading(element, text = 'Loading...') {
    return Utils.showLoading(element, text);
}

function hideLoading(element) {
    return Utils.hideLoading(element);
}

function formatCurrency(amount) {
    return Utils.formatCurrency(amount);
}

function formatDate(dateString) {
    return Utils.formatDate(dateString);
}

function formatDateTime(dateString) {
    return Utils.formatDateTime(dateString);
}

// Export for use in other files
window.Utils = Utils;
window.Auth = Auth;
window.AppState = AppState;
