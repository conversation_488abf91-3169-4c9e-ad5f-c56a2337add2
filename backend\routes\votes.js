const express = require('express');
const { db } = require('../database/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Cast vote for a nominee
router.post('/', authenticateToken, async (req, res) => {
    try {
        const { nominee_id, vote_count = 1 } = req.body;
        const userId = req.user.userId;

        // Validation
        if (!nominee_id || vote_count < 1) {
            return res.status(400).json({
                success: false,
                message: 'Valid nominee ID and vote count are required'
            });
        }

        // Get nominee and event details
        const nominee = await db.get(`
            SELECT 
                n.*,
                e.vote_price,
                e.start_date,
                e.end_date,
                e.is_active as event_active
            FROM nominees n
            JOIN events e ON n.event_id = e.id
            WHERE n.id = ? AND n.is_active = 1
        `, [nominee_id]);

        if (!nominee) {
            return res.status(404).json({
                success: false,
                message: 'Nominee not found'
            });
        }

        if (!nominee.event_active) {
            return res.status(400).json({
                success: false,
                message: 'Event is not active'
            });
        }

        // Check if voting is open
        const now = new Date();
        const startDate = new Date(nominee.start_date);
        const endDate = new Date(nominee.end_date);

        if (now < startDate) {
            return res.status(400).json({
                success: false,
                message: 'Voting has not started yet'
            });
        }

        if (now > endDate) {
            return res.status(400).json({
                success: false,
                message: 'Voting has ended'
            });
        }

        // Calculate total amount
        const totalAmount = parseFloat(nominee.vote_price) * parseInt(vote_count);

        // For now, we'll simulate payment success
        // In a real app, you would integrate with a payment gateway here
        const paymentSuccess = true;

        if (!paymentSuccess) {
            return res.status(400).json({
                success: false,
                message: 'Payment failed'
            });
        }

        // Start transaction
        const queries = [
            {
                sql: `INSERT INTO payments (user_id, event_id, amount, vote_count, payment_status, payment_date)
                      VALUES (?, ?, ?, ?, 'completed', CURRENT_TIMESTAMP)`,
                params: [userId, nominee.event_id, totalAmount, vote_count]
            }
        ];

        const paymentResults = await db.transaction(queries);
        const paymentId = paymentResults[0].id;

        // Record the vote
        await db.run(`
            INSERT INTO votes (user_id, nominee_id, event_id, vote_count, amount_paid, payment_id)
            VALUES (?, ?, ?, ?, ?, ?)
        `, [userId, nominee_id, nominee.event_id, vote_count, totalAmount, paymentId]);

        // Update nominee vote count and total amount
        await db.run(`
            UPDATE nominees 
            SET vote_count = vote_count + ?, 
                total_amount_received = total_amount_received + ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [vote_count, totalAmount, nominee_id]);

        // Update event totals
        await db.run(`
            UPDATE events 
            SET total_votes = total_votes + ?,
                total_revenue = total_revenue + ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [vote_count, totalAmount, nominee.event_id]);

        res.status(201).json({
            success: true,
            message: 'Vote cast successfully',
            vote: {
                nominee_id,
                vote_count,
                amount_paid: totalAmount,
                payment_id: paymentId
            }
        });

    } catch (error) {
        console.error('Cast vote error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get user's voting history
router.get('/my-votes', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const votes = await db.all(`
            SELECT 
                v.*,
                n.name as nominee_name,
                n.profile_image as nominee_image,
                e.title as event_title,
                p.payment_status
            FROM votes v
            JOIN nominees n ON v.nominee_id = n.id
            JOIN events e ON v.event_id = e.id
            LEFT JOIN payments p ON v.payment_id = p.id
            WHERE v.user_id = ?
            ORDER BY v.created_at DESC
            LIMIT ? OFFSET ?
        `, [userId, parseInt(limit), offset]);

        // Get total count
        const { total } = await db.get(
            'SELECT COUNT(*) as total FROM votes WHERE user_id = ?',
            [userId]
        );

        // Get user's voting statistics
        const stats = await db.get(`
            SELECT 
                COUNT(*) as total_votes_cast,
                COALESCE(SUM(vote_count), 0) as total_vote_count,
                COALESCE(SUM(amount_paid), 0) as total_amount_spent,
                COUNT(DISTINCT event_id) as events_participated
            FROM votes
            WHERE user_id = ?
        `, [userId]);

        res.json({
            success: true,
            votes,
            stats,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Get user votes error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get votes for a specific event (public)
router.get('/event/:eventId', async (req, res) => {
    try {
        const eventId = req.params.eventId;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        // Check if event exists
        const event = await db.get('SELECT id FROM events WHERE id = ? AND is_active = 1', [eventId]);
        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Event not found'
            });
        }

        const votes = await db.all(`
            SELECT 
                v.vote_count,
                v.created_at,
                n.name as nominee_name,
                n.profile_image as nominee_image,
                u.name as voter_name
            FROM votes v
            JOIN nominees n ON v.nominee_id = n.id
            LEFT JOIN users u ON v.user_id = u.id
            WHERE v.event_id = ?
            ORDER BY v.created_at DESC
            LIMIT ? OFFSET ?
        `, [eventId, parseInt(limit), offset]);

        // Get total count
        const { total } = await db.get(
            'SELECT COUNT(*) as total FROM votes WHERE event_id = ?',
            [eventId]
        );

        // Get event voting statistics
        const stats = await db.get(`
            SELECT 
                COUNT(*) as total_votes_cast,
                COALESCE(SUM(vote_count), 0) as total_vote_count,
                COALESCE(SUM(amount_paid), 0) as total_revenue,
                COUNT(DISTINCT user_id) as unique_voters,
                COUNT(DISTINCT nominee_id) as nominees_with_votes
            FROM votes
            WHERE event_id = ?
        `, [eventId]);

        res.json({
            success: true,
            votes,
            stats,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Get event votes error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get votes for a specific nominee (public)
router.get('/nominee/:nomineeId', async (req, res) => {
    try {
        const nomineeId = req.params.nomineeId;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        // Check if nominee exists
        const nominee = await db.get('SELECT id FROM nominees WHERE id = ? AND is_active = 1', [nomineeId]);
        if (!nominee) {
            return res.status(404).json({
                success: false,
                message: 'Nominee not found'
            });
        }

        const votes = await db.all(`
            SELECT 
                v.vote_count,
                v.created_at,
                u.name as voter_name
            FROM votes v
            LEFT JOIN users u ON v.user_id = u.id
            WHERE v.nominee_id = ?
            ORDER BY v.created_at DESC
            LIMIT ? OFFSET ?
        `, [nomineeId, parseInt(limit), offset]);

        // Get total count
        const { total } = await db.get(
            'SELECT COUNT(*) as total FROM votes WHERE nominee_id = ?',
            [nomineeId]
        );

        // Get nominee voting statistics
        const stats = await db.get(`
            SELECT 
                COUNT(*) as total_votes_cast,
                COALESCE(SUM(vote_count), 0) as total_vote_count,
                COALESCE(SUM(amount_paid), 0) as total_amount_received,
                COUNT(DISTINCT user_id) as unique_voters
            FROM votes
            WHERE nominee_id = ?
        `, [nomineeId]);

        res.json({
            success: true,
            votes,
            stats,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Get nominee votes error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get voting leaderboard for an event
router.get('/leaderboard/:eventId', async (req, res) => {
    try {
        const eventId = req.params.eventId;

        // Check if event exists
        const event = await db.get('SELECT id, title FROM events WHERE id = ? AND is_active = 1', [eventId]);
        if (!event) {
            return res.status(404).json({
                success: false,
                message: 'Event not found'
            });
        }

        const leaderboard = await db.all(`
            SELECT 
                n.id,
                n.name,
                n.profile_image,
                n.vote_count,
                n.total_amount_received,
                RANK() OVER (ORDER BY n.vote_count DESC) as rank
            FROM nominees n
            WHERE n.event_id = ? AND n.is_active = 1
            ORDER BY n.vote_count DESC, n.total_amount_received DESC
        `, [eventId]);

        res.json({
            success: true,
            event: {
                id: event.id,
                title: event.title
            },
            leaderboard
        });

    } catch (error) {
        console.error('Get leaderboard error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
