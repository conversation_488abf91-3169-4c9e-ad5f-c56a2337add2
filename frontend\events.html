<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events - VoteAwards</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><i class="fas fa-trophy"></i> VoteAwards</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="events.html" class="nav-link active">Events</a>
                <div class="nav-auth" id="nav-auth">
                    <!-- Auth links will be populated by JavaScript -->
                </div>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <main>
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-calendar-alt"></i> Award Events</h1>
                <p>Discover and participate in exciting award events</p>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <div class="filters">
                    <div class="filter-group">
                        <label for="categoryFilter">Category:</label>
                        <select id="categoryFilter">
                            <option value="">All Categories</option>
                            <!-- Categories will be populated by JavaScript -->
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="statusFilter">Status:</label>
                        <select id="statusFilter">
                            <option value="">All Events</option>
                            <option value="upcoming">Upcoming</option>
                            <option value="active">Active</option>
                            <option value="ended">Ended</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="sortFilter">Sort by:</label>
                        <select id="sortFilter">
                            <option value="start_date">Start Date</option>
                            <option value="votes">Most Votes</option>
                            <option value="recent">Recently Added</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <input type="text" id="searchFilter" placeholder="Search events...">
                    </div>
                </div>
            </div>

            <!-- Events Grid -->
            <div class="events-grid" id="eventsGrid">
                <!-- Events will be populated by JavaScript -->
            </div>

            <!-- Pagination -->
            <div class="pagination" id="pagination">
                <!-- Pagination will be populated by JavaScript -->
            </div>

            <!-- Loading State -->
            <div class="loading" id="loading" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i> Loading events...
            </div>

            <!-- Empty State -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-calendar-times"></i>
                <h3>No Events Found</h3>
                <p>There are no events matching your criteria.</p>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 VoteAwards. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/events.js"></script>
</body>
</html>
